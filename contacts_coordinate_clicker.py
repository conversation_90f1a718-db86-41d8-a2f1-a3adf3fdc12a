#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于坐标的联系人点击脚本
功能：由于联系人是渲染元素，使用坐标网格点击方式来操作联系人列表
"""

import win32gui
import pyautogui
import time
import logging
from typing import List, Tuple, Optional

class ContactsCoordinateClicker:
    """基于坐标的联系人点击器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 基于UI树信息的联系人列表区域
        self.contact_area = {
            'x': 1410,      # 列表左边界
            'y': 76,        # 列表上边界
            'width': 520,   # 列表宽度
            'height': 924   # 列表高度
        }
        
        # 联系人项目配置
        self.contact_item_height = 48  # 每个联系人项目的高度
        self.click_offset_x = 60       # 点击位置相对于左边界的偏移（昵称列位置，基于UI树：1460-1400=60）
        self.scroll_step = 1           # 单步滚动步数（精确控制）

        # 固定点击位置配置（单点击模式）
        self.fixed_click_y = self.contact_area['y'] + 200  # 固定Y坐标：列表顶部+200px
        self.single_contact_scroll = 2  # 单个联系人对应的滚动距离
        
        # 点击配置
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.2
        
        self.logger.info("✅ 基于坐标的联系人点击器初始化完成")
    
    def find_contacts_management_window(self) -> Optional[int]:
        """查找通讯录管理窗口句柄"""
        try:
            self.logger.info("🔍 查找通讯录管理窗口...")

            management_hwnd = None

            def enum_windows_callback(hwnd, _):
                nonlocal management_hwnd
                try:
                    if win32gui.IsWindowVisible(hwnd):
                        window_title = win32gui.GetWindowText(hwnd)

                        if "通讯录管理" in window_title:
                            rect = win32gui.GetWindowRect(hwnd)
                            width = rect[2] - rect[0]
                            height = rect[3] - rect[1]

                            if width > 300 and height > 400:
                                management_hwnd = hwnd
                                self.logger.info(f"✅ 找到通讯录管理窗口: '{window_title}' 句柄: {hwnd}")
                                return False

                except Exception as e:
                    self.logger.debug(f"⚠️ 枚举窗口异常: {e}")

                return True

            try:
                win32gui.EnumWindows(enum_windows_callback, None)
            except Exception as enum_error:
                self.logger.warning(f"⚠️ 窗口枚举异常: {enum_error}")
                # 尝试备选方案：直接查找已知的窗口句柄
                return self._find_window_by_title("通讯录管理")

            return management_hwnd

        except Exception as e:
            self.logger.error(f"❌ 查找通讯录管理窗口失败: {e}")
            return None

    def _find_window_by_title(self, title: str) -> Optional[int]:
        """通过标题查找窗口的备选方案"""
        try:
            hwnd = win32gui.FindWindow(None, title)
            if hwnd and win32gui.IsWindowVisible(hwnd):
                self.logger.info(f"✅ 通过标题找到窗口: '{title}' 句柄: {hwnd}")
                return hwnd
            return None
        except Exception as e:
            self.logger.debug(f"⚠️ 通过标题查找窗口失败: {e}")
            return None

    def _activate_window(self, hwnd: int) -> bool:
        """安全地激活窗口"""
        try:
            self.logger.info(f"🔄 激活窗口 {hwnd}...")

            # 方法1: 尝试直接激活
            try:
                win32gui.SetForegroundWindow(hwnd)
                time.sleep(0.5)
                self.logger.info("✅ 窗口激活成功 (方法1)")
                return True
            except Exception as e1:
                self.logger.debug(f"⚠️ 方法1激活失败: {e1}")

            # 方法2: 尝试显示窗口后激活
            try:
                win32gui.ShowWindow(hwnd, 9)  # SW_RESTORE
                time.sleep(0.2)
                win32gui.SetForegroundWindow(hwnd)
                time.sleep(0.5)
                self.logger.info("✅ 窗口激活成功 (方法2)")
                return True
            except Exception as e2:
                self.logger.debug(f"⚠️ 方法2激活失败: {e2}")

            # 方法3: 尝试点击窗口来激活
            try:
                rect = win32gui.GetWindowRect(hwnd)
                center_x = (rect[0] + rect[2]) // 2
                center_y = (rect[1] + rect[3]) // 2

                pyautogui.click(center_x, center_y)
                time.sleep(0.5)
                self.logger.info("✅ 窗口激活成功 (方法3-点击)")
                return True
            except Exception as e3:
                self.logger.debug(f"⚠️ 方法3激活失败: {e3}")

            # 如果所有方法都失败，记录警告但继续执行
            self.logger.warning("⚠️ 所有窗口激活方法都失败，但继续执行")
            return True

        except Exception as e:
            self.logger.error(f"❌ 激活窗口异常: {e}")
            return True  # 返回True以继续执行
    
    def calculate_contact_positions(self) -> List[Tuple[int, int]]:
        """计算联系人项目的点击位置"""
        try:
            positions = []
            
            # 计算可见区域内的联系人位置
            start_y = self.contact_area['y']
            end_y = self.contact_area['y'] + self.contact_area['height']
            click_x = self.contact_area['x'] + self.click_offset_x
            
            # 按联系人项目高度计算位置
            current_y = start_y + (self.contact_item_height // 2)  # 第一个联系人的中心
            
            while current_y < end_y:
                positions.append((click_x, current_y))
                current_y += self.contact_item_height
            
            self.logger.info(f"📐 计算出 {len(positions)} 个联系人位置")
            return positions
            
        except Exception as e:
            self.logger.error(f"❌ 计算联系人位置失败: {e}")
            return []
    
    def click_contact_position(self, x: int, y: int, index: int) -> bool:
        """点击指定位置的联系人"""
        try:
            self.logger.info(f"🖱️ 点击联系人位置 {index}: ({x}, {y})")
            
            # 移动鼠标到位置
            pyautogui.moveTo(x, y, duration=0.3)
            time.sleep(0.1)
            
            # 点击
            pyautogui.click(x, y)
            time.sleep(0.8)  # 等待响应
            
            self.logger.info(f"✅ 成功点击位置 {index}: ({x}, {y})")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 点击位置 {index} 失败: {e}")
            return False
    
    def scroll_contact_list(self, direction: str = "down", steps: int = 3) -> bool:
        """滚动联系人列表"""
        try:
            # 移动鼠标到列表中心
            center_x = self.contact_area['x'] + (self.contact_area['width'] // 2)
            center_y = self.contact_area['y'] + (self.contact_area['height'] // 2)
            
            pyautogui.moveTo(center_x, center_y, duration=0.2)
            time.sleep(0.1)
            
            # 滚动
            if direction == "down":
                pyautogui.scroll(-steps)  # 负数向下滚动
                self.logger.info(f"📜 向下滚动 {steps} 步")
            else:
                pyautogui.scroll(steps)   # 正数向上滚动
                self.logger.info(f"📜 向上滚动 {steps} 步")
            
            time.sleep(0.5)  # 等待滚动完成
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 滚动列表失败: {e}")
            return False
    
    def click_all_contacts_in_view(self) -> int:
        """点击当前视图中的所有联系人"""
        try:
            positions = self.calculate_contact_positions()
            if not positions:
                self.logger.warning("⚠️ 没有计算出联系人位置")
                return 0
            
            success_count = 0
            
            for i, (x, y) in enumerate(positions, 1):
                self.logger.info(f"📋 点击第 {i}/{len(positions)} 个位置")
                
                if self.click_contact_position(x, y, i):
                    success_count += 1
                    # 点击间隔
                    time.sleep(1.0)
                else:
                    self.logger.warning(f"⚠️ 位置 {i} 点击失败")
            
            self.logger.info(f"✅ 当前视图点击完成: 成功 {success_count}/{len(positions)}")
            return success_count
            
        except Exception as e:
            self.logger.error(f"❌ 点击当前视图联系人失败: {e}")
            return 0
    
    def process_all_contacts_with_scroll(self, max_scrolls: int = 10) -> bool:
        """通过滚动处理所有联系人"""
        try:
            self.logger.info("🚀 开始处理所有联系人（包含滚动）...")
            
            # 1. 查找并激活通讯录管理窗口
            management_hwnd = self.find_contacts_management_window()
            if not management_hwnd:
                self.logger.error("❌ 未找到通讯录管理窗口")
                return False
            
            # 激活窗口
            self._activate_window(management_hwnd)
            
            # 2. 滚动到顶部
            self.logger.info("📜 滚动到列表顶部...")
            for _ in range(5):
                self.scroll_contact_list("up", 5)
            
            # 3. 逐页处理联系人
            total_clicks = 0
            
            for scroll_round in range(max_scrolls):
                self.logger.info(f"📋 处理第 {scroll_round + 1}/{max_scrolls} 页联系人")
                
                # 点击当前视图的联系人
                clicks_in_view = self.click_all_contacts_in_view()
                total_clicks += clicks_in_view
                
                if clicks_in_view == 0:
                    self.logger.info("⚠️ 当前视图没有可点击的联系人，可能已到底部")
                    break
                
                # 滚动到下一页（如果不是最后一轮）
                if scroll_round < max_scrolls - 1:
                    self.logger.info("📜 滚动到下一页...")
                    self.scroll_contact_list("down", self.scroll_step)
                    time.sleep(1.0)  # 等待滚动和加载
            
            self.logger.info(f"🎉 所有联系人处理完成: 总共点击 {total_clicks} 次")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 处理所有联系人异常: {e}")
            return False

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('contacts_coordinate_clicker.log', encoding='utf-8')
        ]
    )

def main():
    """主函数"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    print("🎉 基于坐标的联系人点击脚本")
    print("=" * 60)
    
    try:
        # 创建坐标点击器
        clicker = ContactsCoordinateClicker()
        
        # 显示配置信息
        print("📋 联系人列表区域配置:")
        print(f"  位置: ({clicker.contact_area['x']}, {clicker.contact_area['y']})")
        print(f"  尺寸: {clicker.contact_area['width']} x {clicker.contact_area['height']}")
        print(f"  联系人项目高度: {clicker.contact_item_height}")
        print(f"  点击偏移: {clicker.click_offset_x} (昵称列位置)")
        print(f"  实际点击X坐标: {clicker.contact_area['x'] + clicker.click_offset_x}")
        print("📋 基于UI树信息:")
        print("  昵称文本位置: (1460, 44) -> 偏移 = 1460 - 1400 = 60")
        
        # 自动检测和处理，无需人工确认
        print("\n🔍 自动检测通讯录管理窗口状态...")

        # 先检测窗口是否存在
        test_hwnd = clicker.find_contacts_management_window()
        if not test_hwnd:
            print("❌ 未找到通讯录管理窗口")
            print("请确保:")
            print("1. 微信已启动")
            print("2. 通讯录管理窗口已打开")
            print("3. 窗口标题包含'通讯录管理'")
            return

        print(f"✅ 检测到通讯录管理窗口 (句柄: {test_hwnd})")

        # 执行联系人处理
        print("\n🚀 开始自动执行联系人点击流程...")
        print("📋 操作流程:")
        print("  1. 激活通讯录管理窗口")
        print("  2. 滚动到列表顶部")
        print("  3. 逐页点击所有联系人")
        print("  4. 自动滚动到下一页")
        print("  5. 重复直到完成")

        result = clicker.process_all_contacts_with_scroll(max_scrolls=10)
        
        if result:
            print("\n✅ 联系人点击流程执行成功")
        else:
            print("\n⚠️ 联系人点击流程执行失败")
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        logger.info("⚠️ 用户中断操作")
    except Exception as e:
        logger.error(f"❌ 主程序异常: {e}")
        print(f"\n❌ 程序执行异常: {e}")
    
    print("\n" + "=" * 60)
    print("📋 详细日志已保存到: contacts_coordinate_clicker.log")

if __name__ == "__main__":
    main()
